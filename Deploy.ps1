# deploy.ps1

# Optional: Set strict mode
Set-StrictMode -Version Latest

# Step 1: Navigate to script directory
Push-Location -Path $PSScriptRoot

# Step 2: Initialize Terraform
Write-Host "Initializing Terraform..." -ForegroundColor Cyan
terraform init

# Step 3: Create the Terraform plan (optional)
Write-Host "Planning deployment..." -ForegroundColor Cyan
terraform plan -out=tfplan.out

# Step 4: Apply the plan
Write-Host "Applying plan to deploy VM..." -ForegroundColor Cyan
terraform apply "tfplan.out"

# Step 5: Show outputs (e.g., public IP)
Write-Host "Deployment complete. Output:" -ForegroundColor Green
terraform output

# Step 6: Return to original location
Pop-Location
