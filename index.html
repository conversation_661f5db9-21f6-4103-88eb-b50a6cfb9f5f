<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>VM Trial Request</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #f4f6f9;
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    header {
      background-color: #003366;
      color: white;
      width: 100%;
      text-align: center;
      padding: 20px 0;
      font-size: 24px;
      font-weight: bold;
    }

    .container {
      background-color: white;
      padding: 30px 40px;
      margin-top: 40px;
      border-radius: 8px;
      box-shadow: 0 0 12px rgba(0,0,0,0.1);
      max-width: 500px;
      width: 100%;
    }

    form label {
      display: block;
      margin-top: 15px;
      font-size: 15px;
    }

    form input[type="text"],
    form input[type="password"] {
      width: 100%;
      padding: 10px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      margin-top: 5px;
      border-radius: 4px;
      border: 1px solid #ccc;
    }

    button {
      margin-top: 25px;
      width: 100%;
      padding: 12px;
      font-size: 16px;
      font-family: Arial, sans-serif;
      background-color: #0073e6;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }

    button:hover {
      background-color: #005bb5;
    }
  </style>
</head>
<body>

  <header>VM Trial Request</header>

  <div class="container">
    <form id="vmForm">
      <label>Institution Code (4 digits):
        <input type="text" id="institutionCode" maxlength="4" required>
      </label>

      <label>Department Code (3 digits):
        <input type="text" id="departmentCode" maxlength="3" required>
      </label>

      <label>Sequence Number (e.g., 01):
        <input type="text" id="sequence" maxlength="3" required>
      </label>

      <label>Admin Username:
        <input type="text" id="adminUsername" required>
      </label>

      <label>Admin Password:
        <input type="password" id="adminPassword" required>
      </label>

      <button type="submit">Generate Terraform Files</button>
    </form>
  </div>

  <script>
    document.getElementById("vmForm").addEventListener("submit", async function (e) {
      e.preventDefault();

      const inst = document.getElementById("institutionCode").value.trim();
      const dept = document.getElementById("departmentCode").value.trim();
      const seq = document.getElementById("sequence").value.trim();
      const adminUser = document.getElementById("adminUsername").value.trim();
      const adminPass = document.getElementById("adminPassword").value.trim();
      const folderName = `${inst}${dept}${seq}`;

      try {
        const baseDir = await window.showDirectoryPicker({ mode: "readwrite" });
        const subDir = await baseDir.getDirectoryHandle(folderName, { create: true });

        const mainTf = `provider "azurerm" {
  features {}
  subscription_id = "853d88e4-531c-49e8-adb1-fa38e398312e"
}

locals {
  location             = "East US 2"
  resource_group_name  = "\${var.institution_code}-\${var.department_code}-VMC"
  virtual_network_name = "\${var.institution_code}NET"
  vm_name              = "\${var.institution_code}-\${var.department_code}-APP-\${var.sequence}"
}

resource "azurerm_resource_group" "rg" {
  name     = local.resource_group_name
  location = local.location
}

resource "azurerm_virtual_network" "vnet" {
  name                = local.virtual_network_name
  address_space       = ["10.0.0.0/16"]
  location            = local.location
  resource_group_name = azurerm_resource_group.rg.name
}

resource "azurerm_subnet" "subnet" {
  name                 = "\${local.vm_name}-subnet"
  resource_group_name  = azurerm_resource_group.rg.name
  virtual_network_name = azurerm_virtual_network.vnet.name
  address_prefixes     = ["********/24"]
}

resource "azurerm_public_ip" "public_ip" {
  name                = "\${local.vm_name}-pip"
  location            = local.location
  resource_group_name = azurerm_resource_group.rg.name
  allocation_method   = "Static"
  sku                 = "Standard"
}

resource "azurerm_network_interface" "nic" {
  name                = "\${local.vm_name}-nic"
  location            = local.location
  resource_group_name = azurerm_resource_group.rg.name

  ip_configuration {
    name                          = "ipconfig"
    subnet_id                     = azurerm_subnet.subnet.id
    private_ip_address_allocation = "Dynamic"
    public_ip_address_id          = azurerm_public_ip.public_ip.id
  }
}

resource "azurerm_windows_virtual_machine" "vm" {
  name                  = local.vm_name
  location              = local.location
  resource_group_name   = azurerm_resource_group.rg.name
  size                  = "Standard_D4s_v3"
  admin_username        = var.admin_username
  admin_password        = var.admin_password
  network_interface_ids = [azurerm_network_interface.nic.id]

  os_disk {
    name                 = "\${local.vm_name}-osdisk"
    caching              = "ReadWrite"
    storage_account_type = "Premium_LRS"
    disk_size_gb         = 128
  }

  source_image_reference {
    publisher = "MicrosoftWindowsServer"
    offer     = "windowsserver"
    sku       = "2022-datacenter-azure-edition"
    version   = "latest"
  }

  provision_vm_agent = true
}
`;

        const variablesTf = `variable "institution_code" {
  type = string
}

variable "department_code" {
  type = string
}

variable "sequence" {
  type = string
}

variable "admin_username" {
  type = string
}

variable "admin_password" {
  type      = string
  sensitive = true
}
`;

        const tfvars = `institution_code = "${inst}"
department_code  = "${dept}"
sequence         = "${seq}"
admin_username   = "${adminUser}"
admin_password   = "${adminPass}"
`;

        await writeFile(subDir, "main.tf", mainTf);
        await writeFile(subDir, "variables.tf", variablesTf);
        await writeFile(subDir, "terraform.tfvars", tfvars);

        alert(`✅ Terraform files generated in folder: ${folderName}`);
      } catch (err) {
        alert("❌ Error: " + err.message);
        console.error(err);
      }
    });

    async function writeFile(dirHandle, name, content) {
      const fileHandle = await dirHandle.getFileHandle(name, { create: true });
      const writable = await fileHandle.createWritable();
      await writable.write(content);
      await writable.close();
    }
  </script>
</body>
</html>
